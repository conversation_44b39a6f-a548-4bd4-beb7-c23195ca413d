# Seleksi Siswa SMPN 1 Banguntapan

A real-time web app built with Vite, React, and TypeScript to display selection results from Bantul SPMB in a beautiful, professional Material-UI table.

## Fitur
- Menampilkan data seleksi siswa dari endpoint JSON secara real-time (refresh otomatis setiap 5 detik)
- Tabel interaktif dan responsif dengan desain modern
- Menggunakan Material-UI untuk tampilan elegan

## Cara Menjalankan
1. Install dependencies:
   ```powershell
   npm install
   ```
2. Jalankan aplikasi:
   ```powershell
   npm run dev
   ```
3. Buka browser ke URL yang tertera di terminal (biasanya http://localhost:5173)

## Struktur Data
Data diambil dari: https://bantulkab.spmb.id/seleksi/pr/smp/1-22040042-0.json

Kolom tabel:
- Urut
- No Daftar
- Nama
- <PERSON><PERSON>

---

Dibuat dengan ❤️ menggunakan Vite + React + TypeScript + Material-UI.
