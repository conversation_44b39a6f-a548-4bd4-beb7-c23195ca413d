body {
  background: linear-gradient(120deg, #e3f2fd 0%, #f8fafc 100%);
  min-height: 100vh;
  font-family: 'Inter', 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

#root {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem 0;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.MuiTableContainer-root {
  margin-top: 2rem;
}

.MuiTableCell-root {
  font-size: 1.05rem;
}

.MuiTableRow-hover:hover {
  background: #e3f2fd !important;
}
